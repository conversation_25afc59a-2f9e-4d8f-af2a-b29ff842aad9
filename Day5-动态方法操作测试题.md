# Day5 动态方法操作综合测试题

## 📋 测试说明

**测试目标**：全面评估对Objective-C Runtime动态方法操作的理解和掌握程度
**测试时间**：建议60分钟
**总分**：100分
**及格线**：70分

**评分标准**：
- 优秀（90-100分）：深入理解Method Swizzling原理，掌握安全实现，能分析实际应用
- 良好（80-89分）：理解动态方法操作概念，掌握主要API，了解安全注意事项
- 及格（70-79分）：了解基本概念和主要API，能说出应用场景

---

## 第一部分：基础概念理解（20分）

### 1. 单选题（每题2分，共10分）

**1.1** 下列哪个API用于在运行时为类动态添加新方法？
A. `method_addImplementation`
B. `class_addMethod`
C. `class_insertMethod`
D. `runtime_addMethod`

**1.2** `class_addMethod`函数的返回值含义是什么？
A. 返回新添加方法的IMP指针
B. 返回YES表示成功添加，NO表示方法已存在或添加失败
C. 返回方法的SEL选择器
D. 返回类的Method对象

**1.3** `class_replaceMethod`与`class_addMethod`的主要区别是什么？
A. `class_replaceMethod`只能替换实例方法
B. `class_replaceMethod`会返回被替换的原始实现
C. `class_replaceMethod`不需要提供类型编码
D. `class_replaceMethod`只能在+load方法中使用

**1.4** Method Swizzling的核心API是哪个？
A. `method_swizzleImplementations`
B. `method_exchangeImplementations`
C. `method_replaceImplementations`
D. `class_swizzleMethods`

**1.5** 在动态添加方法时，类型编码字符串"v@:"表示什么含义？
A. 返回值为void，参数为id和SEL
B. 返回值为id，参数为void
C. 返回值为SEL，参数为id
D. 返回值为BOOL，参数为id和SEL

### 2. 判断题（每题2分，共10分）

**2.1** 动态添加的方法只在当前运行时有效，应用重启后会消失。（ ）

**2.2** Method Swizzling可以交换任意两个方法的实现，不受类继承关系限制。（ ）

**2.3** 使用`method_exchangeImplementations`交换方法后，原始方法的SEL会发生改变。（ ）

**2.4** 在swizzled方法中调用`[self swizzled_method]`会造成无限递归。（ ）

**2.5** `class_replaceMethod`如果目标方法不存在，会自动添加新方法并返回NULL。（ ）

---

## 第二部分：Method Swizzling深度理解（30分）

### 3. 简答题（每题5分，共15分）

**3.1** 请解释Method Swizzling的工作原理，说明SEL和IMP在交换过程中的变化。（5分）

**3.2** 为什么标准的Method Swizzling实现要先使用`class_addMethod`进行检查，而不是直接使用`method_exchangeImplementations`？（5分）

**3.3** 在swizzled方法实现中，为什么调用`[self swizzled_method]`实际上是调用原始方法而不会造成递归？（5分）

### 4. 代码分析题（15分）

**4.1** 分析下面的Method Swizzling代码，指出其中的问题并提供正确的实现方式。（15分）

```objective-c
@implementation UIViewController (BadSwizzling)

+ (void)load {
    Method originalMethod = class_getInstanceMethod([self class], @selector(viewDidLoad));
    Method swizzledMethod = class_getInstanceMethod([self class], @selector(swizzled_viewDidLoad));
    
    method_exchangeImplementations(originalMethod, swizzledMethod);
}

- (void)swizzled_viewDidLoad {
    NSLog(@"Before viewDidLoad");
    [self swizzled_viewDidLoad];
    NSLog(@"After viewDidLoad");
}

@end
```

**问题分析要点**：
- 缺少哪些安全检查？
- 可能导致什么问题？
- 如何改进？

---

## 第三部分：安全注意事项与最佳实践（25分）

### 5. 多选题（每题3分，共12分）

**5.1** Method Swizzling需要注意哪些安全问题？（多选）
A. 线程安全
B. 继承安全
C. 命名冲突
D. 错误处理
E. 内存泄漏

**5.2** 以下哪些是Method Swizzling的最佳实践？（多选）
A. 在+load方法中执行
B. 使用dispatch_once确保只执行一次
C. 使用Category组织代码
D. 提供开关控制
E. 在+initialize方法中执行

**5.3** 安全的Method Swizzling实现应该包含哪些步骤？（多选）
A. 获取原始方法和swizzled方法
B. 使用class_addMethod尝试添加方法
C. 根据添加结果选择替换或交换策略
D. 检查方法是否获取成功
E. 直接交换方法实现

**5.4** 在什么情况下应该避免使用Method Swizzling？（多选）
A. 系统关键方法
B. 第三方库的私有方法
C. 频繁调用的方法
D. 内存管理相关方法
E. 线程不安全的环境

### 6. 场景分析题（13分）

**6.1** 假设你需要为所有UIViewController的viewDidAppear方法添加统计埋点功能，请设计一个安全的Method Swizzling实现方案。要求包含：（13分）
- 完整的实现代码
- 安全措施说明
- 可能的风险分析

---

## 第四部分：实际应用场景（15分）

### 7. 应用分析题（每题5分，共15分）

**7.1** AOP（面向切面编程）场景：如何使用动态方法操作为所有网络请求添加统一的日志记录功能？请说明技术方案和实现要点。（5分）

**7.2** 调试工具开发场景：如何利用动态方法操作开发一个内存泄漏检测工具，自动追踪对象的创建和释放？（5分）

**7.3** Bug修复场景：假设系统的某个API在特定条件下会崩溃，如何使用动态方法操作进行热修复？请说明具体步骤。（5分）

---

## 第五部分：知识关联与综合应用（10分）

### 8. 综合分析题（10分）

**8.1** 结合前四天的Runtime知识，分析动态方法操作在整个Runtime体系中的作用：（10分）

**要求分析以下关联关系**：
- 与消息发送机制的关系（2分）
- 与消息转发机制的关系（2分）
- 与内存管理的关系（2分）
- 与类结构的关系（2分）
- 在实际开发中的综合应用价值（2分）

---

## 📝 参考答案与解析

### 第一部分答案

**1.1** B - `class_addMethod`是Runtime提供的动态添加方法的标准API

**1.2** B - 返回BOOL值，YES表示成功添加，NO表示方法已存在或添加失败

**1.3** B - `class_replaceMethod`会返回被替换的原始IMP，而`class_addMethod`返回BOOL

**1.4** B - `method_exchangeImplementations`是Method Swizzling的核心API

**1.5** A - "v@:"表示返回void，参数为id(self)和SEL(_cmd)

**2.1** ✓ - 动态添加的方法只在当前运行时有效

**2.2** ✗ - 需要考虑继承关系和方法存在性

**2.3** ✗ - SEL不变，只是IMP指向发生了交换

**2.4** ✗ - 由于方法交换，调用的实际是原始实现

**2.5** ✓ - 这是`class_replaceMethod`的特性

### 第二部分答案

**3.1** Method Swizzling通过交换两个方法的IMP实现，SEL保持不变，但指向的IMP发生交换，从而改变方法调用的实际行为。

**3.2** 为了处理继承关系，确保在子类中不会重复swizzling，避免影响父类的方法实现。

**3.3** 因为方法交换后，swizzled_method的SEL实际指向了原始方法的IMP，所以调用的是原始实现。

**4.1** 问题：缺少线程安全保护、方法存在性检查、继承安全处理。应该使用dispatch_once、class_addMethod检查等。

### 第三部分答案

**5.1** A,B,C,D - 线程安全、继承安全、命名冲突、错误处理都是重要的安全考虑

**5.2** A,B,C,D - 除了E（应该在+load而不是+initialize中执行）

**5.3** A,B,C,D - 完整的安全实现流程

**5.4** A,B,D - 系统关键方法、第三方私有方法、内存管理方法风险较高

### 评分细则

- **基础概念（20分）**：测试对API和基本概念的理解
- **Method Swizzling（30分）**：重点测试核心技术的掌握程度
- **安全实践（25分）**：评估安全意识和最佳实践掌握
- **应用场景（15分）**：测试实际应用能力
- **知识关联（10分）**：评估知识体系的完整性

### 第四部分详细答案

**7.1 AOP网络请求日志**：
```objective-c
@implementation NSURLSession (Logging)
+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        Class class = [self class];
        SEL originalSelector = @selector(dataTaskWithRequest:completionHandler:);
        SEL swizzledSelector = @selector(logging_dataTaskWithRequest:completionHandler:);

        Method originalMethod = class_getInstanceMethod(class, originalSelector);
        Method swizzledMethod = class_getInstanceMethod(class, swizzledSelector);

        BOOL didAddMethod = class_addMethod(class, originalSelector,
                                           method_getImplementation(swizzledMethod),
                                           method_getTypeEncoding(swizzledMethod));
        if (didAddMethod) {
            class_replaceMethod(class, swizzledSelector,
                               method_getImplementation(originalMethod),
                               method_getTypeEncoding(originalMethod));
        } else {
            method_exchangeImplementations(originalMethod, swizzledMethod);
        }
    });
}

- (NSURLSessionDataTask *)logging_dataTaskWithRequest:(NSURLRequest *)request
                                    completionHandler:(void (^)(NSData *, NSURLResponse *, NSError *))completionHandler {
    NSLog(@"🌐 网络请求开始: %@", request.URL);
    NSDate *startTime = [NSDate date];

    return [self logging_dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        NSTimeInterval duration = [[NSDate date] timeIntervalSinceDate:startTime];
        NSLog(@"🌐 网络请求完成: %@ 耗时: %.2fs", request.URL, duration);
        if (completionHandler) {
            completionHandler(data, response, error);
        }
    }];
}
@end
```

**7.2 内存泄漏检测工具**：
```objective-c
@implementation NSObject (LeakDetector)
+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        [self swizzleAllocAndDealloc];
    });
}

+ (void)swizzleAllocAndDealloc {
    // Swizzle alloc
    Method allocMethod = class_getClassMethod([self class], @selector(alloc));
    Method swizzledAllocMethod = class_getClassMethod([self class], @selector(leak_alloc));
    method_exchangeImplementations(allocMethod, swizzledAllocMethod);

    // Swizzle dealloc
    Method deallocMethod = class_getInstanceMethod([self class], @selector(dealloc));
    Method swizzledDeallocMethod = class_getInstanceMethod([self class], @selector(leak_dealloc));
    method_exchangeImplementations(deallocMethod, swizzledDeallocMethod);
}

+ (instancetype)leak_alloc {
    id instance = [self leak_alloc];
    [[LeakTracker shared] trackAllocation:instance];
    return instance;
}

- (void)leak_dealloc {
    [[LeakTracker shared] trackDeallocation:self];
    [self leak_dealloc];
}
@end
```

**7.3 Bug热修复方案**：
```objective-c
@implementation BugFixManager
+ (void)applyHotFix {
    // 假设修复UIButton的某个崩溃bug
    Class buttonClass = [UIButton class];
    SEL buggySelector = @selector(setTitle:forState:);
    SEL fixedSelector = @selector(fixed_setTitle:forState:);

    // 添加修复方法
    Method fixedMethod = class_getInstanceMethod([self class], fixedSelector);
    BOOL success = class_addMethod(buttonClass, fixedSelector,
                                  method_getImplementation(fixedMethod),
                                  method_getTypeEncoding(fixedMethod));

    if (success) {
        // 替换原始方法
        Method originalMethod = class_getInstanceMethod(buttonClass, buggySelector);
        method_exchangeImplementations(originalMethod,
                                     class_getInstanceMethod(buttonClass, fixedSelector));
    }
}

- (void)fixed_setTitle:(NSString *)title forState:(UIControlState)state {
    // 添加安全检查
    if (title == nil) {
        title = @"";
    }
    // 调用原始实现
    [self fixed_setTitle:title forState:state];
}
@end
```

### 第五部分详细答案

**8.1 知识关联分析**：

**与消息发送机制的关系（2分）**：
动态方法操作直接影响`objc_msgSend`的查找过程。当动态添加方法后，这些方法会被加入到类的方法列表中，在消息发送时能够被正常查找到。Method Swizzling改变了SEL到IMP的映射关系，影响最终调用的方法实现。

**与消息转发机制的关系（2分）**：
动态方法操作可以在消息转发的第一阶段（`resolveInstanceMethod`）中使用，通过`class_addMethod`动态添加方法实现，避免进入后续的转发流程。这是一种更高效的消息处理方式。

**与内存管理的关系（2分）**：
在进行Method Swizzling时，特别是涉及`dealloc`、`retain`、`release`等内存管理方法时，需要格外小心。错误的swizzling可能导致内存泄漏或过度释放。同时，swizzled方法中的对象引用也需要正确管理。

**与类结构的关系（2分）**：
动态方法操作基于类的方法列表结构。`class_addMethod`会修改类的方法列表，而Method Swizzling则是修改方法列表中方法的IMP指向。这些操作都是对类结构的运行时修改。

**综合应用价值（2分）**：
动态方法操作是Runtime最实用的功能之一，广泛应用于AOP编程、统计埋点、Bug修复、调试工具等场景。它提供了在不修改原始代码的情况下扩展和修改功能的能力，是iOS开发中实现横切关注点的重要技术手段。

---

## 🎯 进阶挑战题（附加10分）

### 9. 高级应用题（10分）

**9.1** 设计一个通用的Method Swizzling工具类，要求：（10分）
- 支持实例方法和类方法的swizzling
- 提供安全检查和错误处理
- 支持swizzling的撤销操作
- 提供调试信息输出

**参考实现框架**：
```objective-c
@interface MethodSwizzler : NSObject
+ (BOOL)swizzleInstanceMethod:(SEL)originalSEL
                    withMethod:(SEL)swizzledSEL
                       inClass:(Class)targetClass;
+ (BOOL)swizzleClassMethod:(SEL)originalSEL
                withMethod:(SEL)swizzledSEL
                   inClass:(Class)targetClass;
+ (BOOL)restoreMethod:(SEL)selector inClass:(Class)targetClass;
+ (void)enableDebugMode:(BOOL)enabled;
@end
```

---

## 📊 测试评分标准详解

### 分数等级划分

**优秀级别（90-100分）**：
- 深入理解Method Swizzling的工作原理和底层机制
- 熟练掌握安全实现模式和最佳实践
- 能够分析复杂的应用场景并提供解决方案
- 理解动态方法操作与其他Runtime知识的关联
- 具备独立设计和实现动态方法操作方案的能力

**良好级别（80-89分）**：
- 理解动态方法操作的基本概念和主要API
- 掌握Method Swizzling的标准实现模式
- 了解主要的安全注意事项和最佳实践
- 能够应用于常见的开发场景
- 理解与其他Runtime知识的基本关联

**及格级别（70-79分）**：
- 了解动态方法操作的作用和价值
- 知道主要的API和基本使用方法
- 了解Method Swizzling的基本概念
- 能够说出主要的应用场景
- 对安全注意事项有基本认识

**不及格（70分以下）**：
- 对基本概念理解不清
- 不能正确使用主要API
- 缺乏安全意识
- 无法应用于实际场景

### 各部分权重说明

1. **基础概念理解（20%）**：考查对API和基本概念的掌握
2. **Method Swizzling深度理解（30%）**：重点考查核心技术
3. **安全注意事项与最佳实践（25%）**：考查实际应用能力
4. **实际应用场景（15%）**：考查解决问题的能力
5. **知识关联与综合应用（10%）**：考查知识体系完整性

**通过这套全面的测试题，可以准确评估学习者对Day5动态方法操作内容的掌握程度，确保达到预期的学习目标，为后续的Runtime学习奠定坚实基础。**
