# 动态方法操作详解

## 概述

动态方法操作是Objective-C Runtime最强大和最常用的功能之一。它允许在运行时动态地添加、替换和交换方法实现，为iOS开发提供了极大的灵活性。这种能力使得Method Swizzling、AOP编程、热修复等高级技术成为可能。

核心功能包括：
- **动态添加方法**：在运行时为类添加新的方法实现
- **方法替换**：替换现有方法的实现
- **方法交换**：交换两个方法的实现（Method Swizzling）
- **方法实现获取和设置**：动态获取和修改方法的实现指针

## 动态方法操作API

### 方法添加API

**class_addMethod函数**
```objective-c
BOOL class_addMethod(Class cls, SEL name, IMP imp, const char *types);
```

参数说明：
- `cls`：要添加方法的目标类
- `name`：方法选择器（方法名）
- `imp`：方法实现的函数指针
- `types`：方法的类型编码字符串

返回值：
- `YES`：成功添加方法
- `NO`：方法已存在或添加失败

**使用示例：**
```objective-c
// 定义方法实现
void dynamicMethodImplementation(id self, SEL _cmd) {
    NSLog(@"Dynamic method called on %@", self);
}

// 添加方法到类
Class targetClass = [MyClass class];
SEL methodSelector = @selector(dynamicMethod);
BOOL success = class_addMethod(targetClass, 
                              methodSelector, 
                              (IMP)dynamicMethodImplementation, 
                              "v@:");

if (success) {
    NSLog(@"Method added successfully");
} else {
    NSLog(@"Method already exists or failed to add");
}
```

### 方法替换API

**class_replaceMethod函数**
```objective-c
IMP class_replaceMethod(Class cls, SEL name, IMP imp, const char *types);
```

参数说明：
- `cls`：目标类
- `name`：要替换的方法选择器
- `imp`：新的方法实现
- `types`：方法类型编码

返回值：
- 返回被替换的原始方法实现（IMP）
- 如果方法不存在，则添加新方法并返回NULL

**使用示例：**
```objective-c
// 新的方法实现
void newMethodImplementation(id self, SEL _cmd) {
    NSLog(@"New implementation called");
}

// 替换方法实现
IMP originalIMP = class_replaceMethod([MyClass class],
                                     @selector(originalMethod),
                                     (IMP)newMethodImplementation,
                                     "v@:");

// 保存原始实现以备后用
if (originalIMP) {
    NSLog(@"Original method replaced");
}
```

### 方法交换API

**method_exchangeImplementations函数**
```objective-c
void method_exchangeImplementations(Method m1, Method m2);
```

参数说明：
- `m1`：第一个方法对象
- `m2`：第二个方法对象

这是Method Swizzling的核心API，用于交换两个方法的实现。

**使用示例：**
```objective-c
// 获取要交换的方法
Method originalMethod = class_getInstanceMethod([UIViewController class], 
                                               @selector(viewDidLoad));
Method swizzledMethod = class_getInstanceMethod([UIViewController class], 
                                               @selector(swizzled_viewDidLoad));

// 交换方法实现
method_exchangeImplementations(originalMethod, swizzledMethod);
```

### 方法实现操作API

**获取和设置方法实现：**
```objective-c
// 获取方法实现
IMP method_getImplementation(Method m);

// 设置方法实现
IMP method_setImplementation(Method m, IMP imp);

// 直接获取类的方法实现
IMP class_getMethodImplementation(Class cls, SEL name);
```

**使用示例：**
```objective-c
// 获取方法对象
Method method = class_getInstanceMethod([MyClass class], @selector(myMethod));

// 获取当前实现
IMP currentIMP = method_getImplementation(method);

// 设置新实现
IMP oldIMP = method_setImplementation(method, newImplementation);
```

## Method Swizzling技术详解

### Method Swizzling原理

Method Swizzling是利用Objective-C Runtime的动态特性，在运行时交换两个方法实现的技术。其工作原理基于以下机制：

1. **方法查找机制**：Objective-C的方法调用通过选择器（SEL）查找对应的实现（IMP）
2. **动态绑定**：SEL和IMP的绑定关系可以在运行时修改
3. **方法交换**：通过交换两个方法的IMP，实现方法行为的替换

**技术原理图：**
```
交换前：
SEL(originalMethod) -> IMP(originalImplementation)
SEL(swizzledMethod) -> IMP(swizzledImplementation)

交换后：
SEL(originalMethod) -> IMP(swizzledImplementation)
SEL(swizzledMethod) -> IMP(originalImplementation)
```

### Method Swizzling实现模式

**标准实现模式：**
```objective-c
@implementation UIViewController (Swizzling)

+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        Class class = [self class];
        
        SEL originalSelector = @selector(viewDidLoad);
        SEL swizzledSelector = @selector(swizzled_viewDidLoad);
        
        Method originalMethod = class_getInstanceMethod(class, originalSelector);
        Method swizzledMethod = class_getInstanceMethod(class, swizzledSelector);
        
        // 尝试添加方法，如果已存在则返回NO
        BOOL didAddMethod = class_addMethod(class,
                                           originalSelector,
                                           method_getImplementation(swizzledMethod),
                                           method_getTypeEncoding(swizzledMethod));
        
        if (didAddMethod) {
            // 如果成功添加，则替换swizzled方法的实现
            class_replaceMethod(class,
                               swizzledSelector,
                               method_getImplementation(originalMethod),
                               method_getTypeEncoding(originalMethod));
        } else {
            // 如果方法已存在，直接交换实现
            method_exchangeImplementations(originalMethod, swizzledMethod);
        }
    });
}

- (void)swizzled_viewDidLoad {
    // 调用原始实现（注意：由于方法交换，这里实际调用的是原始的viewDidLoad）
    [self swizzled_viewDidLoad];
    
    // 添加自定义逻辑
    NSLog(@"ViewDidLoad swizzled for %@", NSStringFromClass([self class]));
}

@end
```

### Method Swizzling安全注意事项

**1. 线程安全**
```objective-c
// 使用dispatch_once确保只执行一次
static dispatch_once_t onceToken;
dispatch_once(&onceToken, ^{
    // Swizzling代码
});
```

**2. 继承安全**
```objective-c
// 检查方法是否存在，避免在子类中重复swizzling
BOOL didAddMethod = class_addMethod(class,
                                   originalSelector,
                                   method_getImplementation(swizzledMethod),
                                   method_getTypeEncoding(swizzledMethod));
```

**3. 命名规范**
```objective-c
// 使用前缀避免命名冲突
- (void)myapp_swizzled_viewDidLoad {
    [self myapp_swizzled_viewDidLoad]; // 调用原始实现
    // 自定义逻辑
}
```

**4. 错误处理**
```objective-c
// 检查方法是否获取成功
if (!originalMethod || !swizzledMethod) {
    NSLog(@"Method swizzling failed: method not found");
    return;
}
```

## 动态方法操作的应用场景

### AOP（面向切面编程）
```objective-c
// 为所有网络请求添加统一的日志记录
@implementation NSURLSession (Logging)

+ (void)load {
    // Swizzle dataTaskWithRequest:completionHandler:
    // 在请求前后添加日志记录逻辑
}

@end
```

### 统计埋点
```objective-c
// 自动统计页面访问
@implementation UIViewController (Analytics)

+ (void)load {
    // Swizzle viewDidAppear:
    // 自动发送页面访问统计
}

@end
```

### Bug修复和功能增强
```objective-c
// 修复系统API的bug或添加功能
@implementation UIButton (Enhancement)

+ (void)load {
    // Swizzle setTitle:forState:
    // 添加额外的功能或修复bug
}

@end
```

### 调试和开发工具
```objective-c
// 开发阶段的调试辅助
@implementation NSObject (DebugHelper)

+ (void)load {
    #ifdef DEBUG
    // Swizzle dealloc方法，追踪对象释放
    #endif
}

@end
```

## 与前四天知识的关联

### 与消息发送机制的关系
动态方法操作直接影响消息发送的查找过程：
```objective-c
// objc_msgSend查找流程中的方法实现获取
IMP lookUpImpOrForward(id inst, SEL sel, Class cls, int behavior) {
    // 1. 缓存查找
    // 2. 方法列表查找 <- 动态添加的方法在这里被找到
    // 3. 父类查找
    // 4. 消息转发
}
```

### 与消息转发机制的关系
动态方法操作可以避免进入消息转发流程：
```objective-c
// 在resolveInstanceMethod中动态添加方法
+ (BOOL)resolveInstanceMethod:(SEL)sel {
    if (sel == @selector(dynamicMethod)) {
        class_addMethod([self class], sel, (IMP)dynamicImplementation, "v@:");
        return YES;
    }
    return [super resolveInstanceMethod:sel];
}
```

### 与内存管理的关系
Method Swizzling需要注意内存管理：
```objective-c
// 在swizzled方法中正确处理内存管理
- (void)swizzled_dealloc {
    // 自定义清理逻辑
    [self swizzled_dealloc]; // 调用原始dealloc
}
```

### 与类结构的关系
动态方法操作修改类的方法列表：
```objective-c
// 动态添加的方法会被加入到类的方法列表中
// 可以通过class_copyMethodList查看到新添加的方法
```

## 最佳实践和注意事项

### 最佳实践

**1. 在+load方法中进行Swizzling**
```objective-c
+ (void)load {
    // +load方法在类加载时调用，确保Swizzling在使用前完成
}
```

**2. 使用Category组织Swizzling代码**
```objective-c
@interface UIViewController (MyAppSwizzling)
@end

@implementation UIViewController (MyAppSwizzling)
// Swizzling实现
@end
```

**3. 提供开关控制**
```objective-c
+ (void)load {
    if ([MyAppConfig isSwizzlingEnabled]) {
        // 执行Swizzling
    }
}
```

### 风险和注意事项

**1. 影响系统稳定性**
- 不当的Swizzling可能导致系统崩溃
- 需要充分测试各种场景

**2. 调试困难**
- Swizzling后的调用栈可能令人困惑
- 需要良好的日志记录

**3. 版本兼容性**
- 系统更新可能改变内部实现
- 需要针对不同iOS版本进行测试

**4. 性能影响**
- 过度使用可能影响性能
- 需要权衡功能和性能

## 总结

动态方法操作是Objective-C Runtime的核心功能，为iOS开发提供了强大的动态能力：

1. **class_addMethod**：动态添加方法，支持运行时扩展类功能
2. **class_replaceMethod**：替换方法实现，支持功能修改和增强
3. **method_exchangeImplementations**：方法交换，Method Swizzling的核心
4. **Method Swizzling**：最常用的Runtime技术，广泛应用于AOP、统计、调试等场景

掌握这些技术需要深入理解Runtime的工作原理，同时要注意安全性和最佳实践，确保代码的稳定性和可维护性。

## Swift Playground验证代码

### 使用说明
将以下代码复制到 Xcode Playground（选择 macOS 平台）中运行，观察动态方法操作的工作过程。

### 完整验证代码

```swift
import Foundation

// =============================================================================
// Day 5: 动态方法操作验证代码
// 通过Swift代码演示Objective-C Runtime的动态方法操作功能
// =============================================================================

print("=== Day 5: 动态方法操作演示 ===\n")

// =============================================================================
// 1. 动态添加方法演示
// =============================================================================

print("1️⃣ 动态添加方法演示:")

class DynamicClass: NSObject {
    @objc var name: String

    @objc init(name: String) {
        self.name = name
        super.init()
        print("   🎯 DynamicClass实例被创建: \(name)")
    }

    @objc func originalMethod() {
        print("   📞 调用原始方法: \(name)")
    }
}

// 定义要动态添加的方法实现
let dynamicMethodImplementation: @convention(c) (AnyObject, Selector) -> Void = { (self, _cmd) in
    let obj = self as! DynamicClass
    print("   ✨ 动态添加的方法被调用: \(obj.name)")
}

print("为DynamicClass动态添加方法:")
let dynamicClass = object_getClass(DynamicClass.self)!
let dynamicSelector = sel_registerName("dynamicMethod")

// 使用class_addMethod添加方法
let addSuccess = class_addMethod(dynamicClass,
                                dynamicSelector,
                                unsafeBitCast(dynamicMethodImplementation, to: IMP.self),
                                "v@:")

if addSuccess {
    print("   ✅ 方法添加成功")

    // 创建实例并调用动态添加的方法
    let instance = DynamicClass(name: "TestObject")

    // 验证方法是否存在
    if instance.responds(to: dynamicSelector) {
        print("   📋 实例响应动态方法")
        instance.perform(dynamicSelector)
    }
} else {
    print("   ❌ 方法添加失败")
}

print("\n" + String(repeating: "-", count: 50))

// =============================================================================
// 2. 方法替换演示
// =============================================================================

print("\n2️⃣ 方法替换演示:")

class ReplaceClass: NSObject {
    @objc var identifier: String

    @objc init(identifier: String) {
        self.identifier = identifier
        super.init()
    }

    @objc func greet() {
        print("   👋 原始问候: Hello from \(identifier)")
    }
}

// 定义新的方法实现
let newGreetImplementation: @convention(c) (AnyObject, Selector) -> Void = { (self, _cmd) in
    let obj = self as! ReplaceClass
    print("   🎉 新的问候: Hi there from \(obj.identifier)!")
}

print("替换ReplaceClass的greet方法:")
let replaceClass = object_getClass(ReplaceClass.self)!
let greetSelector = sel_registerName("greet")

// 创建实例并调用原始方法
let replaceInstance = ReplaceClass(identifier: "Original")
print("   调用原始方法:")
replaceInstance.greet()

// 使用class_replaceMethod替换方法实现
let originalIMP = class_replaceMethod(replaceClass,
                                     greetSelector,
                                     unsafeBitCast(newGreetImplementation, to: IMP.self),
                                     "v@:")

if originalIMP != nil {
    print("   ✅ 方法替换成功")
    print("   调用替换后的方法:")
    replaceInstance.greet()
} else {
    print("   ❌ 方法替换失败")
}

print("\n" + String(repeating: "-", count: 50))

// =============================================================================
// 3. Method Swizzling演示
// =============================================================================

print("\n3️⃣ Method Swizzling演示:")

class SwizzleClass: NSObject {
    @objc var title: String

    @objc init(title: String) {
        self.title = title
        super.init()
    }

    @objc func performAction() {
        print("   🎬 执行原始动作: \(title)")
    }

    @objc func swizzledAction() {
        // 注意：由于方法交换，这里调用的实际是原始的performAction
        self.swizzledAction()
        print("   ⚡ 执行Swizzled逻辑: 为\(title)添加额外功能")
    }
}

print("演示Method Swizzling:")
let swizzleClass = object_getClass(SwizzleClass.self)!

// 获取要交换的方法
let originalActionMethod = class_getInstanceMethod(swizzleClass, sel_registerName("performAction"))
let swizzledActionMethod = class_getInstanceMethod(swizzleClass, sel_registerName("swizzledAction"))

if let original = originalActionMethod, let swizzled = swizzledActionMethod {
    print("   📋 获取到要交换的方法")

    // 创建实例并调用原始方法
    let swizzleInstance = SwizzleClass(title: "TestAction")
    print("   交换前调用performAction:")
    swizzleInstance.performAction()

    // 执行方法交换
    method_exchangeImplementations(original, swizzled)
    print("   ✅ 方法交换完成")

    print("   交换后调用performAction:")
    swizzleInstance.performAction()
} else {
    print("   ❌ 无法获取方法，交换失败")
}

print("\n" + String(repeating: "-", count: 50))

// =============================================================================
// 4. 方法实现获取和设置演示
// =============================================================================

print("\n4️⃣ 方法实现获取和设置演示:")

class IMPClass: NSObject {
    @objc var value: Int

    @objc init(value: Int) {
        self.value = value
        super.init()
    }

    @objc func calculate() -> Int {
        print("   🧮 原始计算: \(value) * 2")
        return value * 2
    }
}

// 定义新的计算实现
let newCalculateImplementation: @convention(c) (AnyObject, Selector) -> Int = { (self, _cmd) in
    let obj = self as! IMPClass
    print("   ✨ 新的计算: \(obj.value) * 3")
    return obj.value * 3
}

print("演示方法实现的获取和设置:")
let impClass = object_getClass(IMPClass.self)!
let calculateSelector = sel_registerName("calculate")

// 获取方法对象
if let method = class_getInstanceMethod(impClass, calculateSelector) {
    print("   📋 获取到calculate方法")

    // 创建实例并调用原始方法
    let impInstance = IMPClass(value: 5)
    print("   原始实现结果: \(impInstance.calculate())")

    // 获取当前方法实现
    let currentIMP = method_getImplementation(method)
    print("   ✅ 获取到当前方法实现")

    // 设置新的方法实现
    let oldIMP = method_setImplementation(method, unsafeBitCast(newCalculateImplementation, to: IMP.self))
    print("   ✅ 设置新的方法实现")

    print("   新实现结果: \(impInstance.calculate())")

    // 恢复原始实现
    method_setImplementation(method, oldIMP)
    print("   🔄 恢复原始实现")
    print("   恢复后结果: \(impInstance.calculate())")
} else {
    print("   ❌ 无法获取方法")
}

print("\n" + String(repeating: "-", count: 50))

// =============================================================================
// 5. 安全的Method Swizzling实现演示
// =============================================================================

print("\n5️⃣ 安全的Method Swizzling实现演示:")

class SafeSwizzleClass: NSObject {
    @objc var name: String

    @objc init(name: String) {
        self.name = name
        super.init()
    }

    @objc func safeMethod() {
        print("   🔒 安全方法调用: \(name)")
    }

    @objc func safeSwizzledMethod() {
        // 调用原始实现
        self.safeSwizzledMethod()
        print("   🛡️ 安全Swizzling逻辑: 为\(name)添加保护")
    }
}

func performSafeSwizzling() {
    print("演示安全的Method Swizzling实现:")

    let targetClass = SafeSwizzleClass.self
    let originalSelector = sel_registerName("safeMethod")
    let swizzledSelector = sel_registerName("safeSwizzledMethod")

    let originalMethod = class_getInstanceMethod(targetClass, originalSelector)
    let swizzledMethod = class_getInstanceMethod(targetClass, swizzledSelector)

    guard let original = originalMethod, let swizzled = swizzledMethod else {
        print("   ❌ 无法获取方法，Swizzling失败")
        return
    }

    // 安全的Swizzling实现
    let didAddMethod = class_addMethod(targetClass,
                                      originalSelector,
                                      method_getImplementation(swizzled),
                                      method_getTypeEncoding(swizzled))

    if didAddMethod {
        // 如果成功添加，则替换swizzled方法的实现
        class_replaceMethod(targetClass,
                           swizzledSelector,
                           method_getImplementation(original),
                           method_getTypeEncoding(original))
        print("   ✅ 安全Swizzling完成（添加模式）")
    } else {
        // 如果方法已存在，直接交换实现
        method_exchangeImplementations(original, swizzled)
        print("   ✅ 安全Swizzling完成（交换模式）")
    }

    // 测试Swizzling效果
    let safeInstance = SafeSwizzleClass(name: "SafeTest")
    print("   测试Swizzling效果:")
    safeInstance.safeMethod()
}

// 执行安全Swizzling
performSafeSwizzling()

print("\n" + String(repeating: "-", count: 50))

// =============================================================================
// 6. 动态方法操作的实际应用场景演示
// =============================================================================

print("\n6️⃣ 实际应用场景演示:")

// 模拟统计埋点的应用场景
class ViewController: NSObject {
    @objc var viewName: String

    @objc init(viewName: String) {
        self.viewName = viewName
        super.init()
    }

    @objc func viewDidAppear() {
        print("   📱 页面出现: \(viewName)")
    }

    @objc func analytics_viewDidAppear() {
        // 调用原始实现
        self.analytics_viewDidAppear()

        // 添加统计逻辑
        print("   📊 统计埋点: 记录\(viewName)页面访问")
        // 这里可以发送统计数据到服务器
    }
}

print("模拟统计埋点的Method Swizzling应用:")

// 为ViewController添加统计功能
let vcClass = ViewController.self
let viewDidAppearSelector = sel_registerName("viewDidAppear")
let analyticsSelector = sel_registerName("analytics_viewDidAppear")

let originalVCMethod = class_getInstanceMethod(vcClass, viewDidAppearSelector)
let analyticsMethod = class_getInstanceMethod(vcClass, analyticsSelector)

if let original = originalVCMethod, let analytics = analyticsMethod {
    method_exchangeImplementations(original, analytics)
    print("   ✅ 统计埋点Swizzling完成")

    // 测试统计功能
    let homeVC = ViewController(viewName: "首页")
    let profileVC = ViewController(viewName: "个人中心")

    print("   测试页面访问统计:")
    homeVC.viewDidAppear()
    profileVC.viewDidAppear()
} else {
    print("   ❌ 统计埋点Swizzling失败")
}

print("\n" + String(repeating: "-", count: 50))

// =============================================================================
// 7. 学习要点总结
// =============================================================================

print("\n📚 Day 5 学习要点总结:")
print(String(repeating: "=", count: 60))

print("\n🎯 核心概念:")
print("1. 动态方法添加")
print("   - class_addMethod：为类添加新方法")
print("   - 支持运行时扩展类功能")
print("   - 需要提供方法实现和类型编码")

print("\n2. 方法替换")
print("   - class_replaceMethod：替换现有方法实现")
print("   - 返回原始实现以备后用")
print("   - 支持功能修改和增强")

print("\n3. Method Swizzling")
print("   - method_exchangeImplementations：交换方法实现")
print("   - Runtime最常用的技术之一")
print("   - 广泛应用于AOP、统计、调试等场景")

print("\n4. 方法实现操作")
print("   - method_getImplementation：获取方法实现")
print("   - method_setImplementation：设置方法实现")
print("   - 支持更精细的方法控制")

print("\n🔗 与前四天知识的关联:")
print("   - 基于Day 4的类结构和方法列表组织")
print("   - 影响Day 1的消息发送查找过程")
print("   - 可以避免Day 2的消息转发流程")
print("   - 需要注意Day 3的内存管理问题")

print("\n⚠️ 安全注意事项:")
print("   - 使用dispatch_once确保线程安全")
print("   - 检查方法存在性避免继承问题")
print("   - 使用前缀避免命名冲突")
print("   - 充分测试确保系统稳定性")

print("\n🚀 实际应用场景:")
print("   - AOP面向切面编程")
print("   - 统计埋点和数据收集")
print("   - Bug修复和功能增强")
print("   - 调试工具和开发辅助")

print("\n=== Day 5 动态方法操作演示完成 ===")
print("✅ 您已经通过代码演示掌握了动态方法操作的核心技术！")
```
